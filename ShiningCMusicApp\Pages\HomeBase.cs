using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;

namespace ShiningCMusicApp.Pages;

public class HomeBase : ComponentBase
{
    [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;
    [Inject] protected NavigationManager Navigation { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        // Any initialization logic can go here
        await JSRuntime.InvokeVoidAsync("console.log", "Home page initialized");
    }

    protected void NavigateToLessons()
    {
        Navigation.NavigateTo("/lessons");
    }

    protected void NavigateToTutors()
    {
        Navigation.NavigateTo("/tutors");
    }

    protected void NavigateToStudents()
    {
        Navigation.NavigateTo("/students");
    }

    protected void NavigateToAdmin()
    {
        Navigation.NavigateTo("/admin");
    }
}
