@page "/admin"
@using ShiningCMusicCommon.Models
@using ShiningCMusicCommon.Enums
@using ShiningCMusicCommon.Utilities
@using ShiningCMusicApp.Services
@using ShiningCMusicApp.Services.Interfaces
@using ShiningCMusicApp.Components
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.DropDowns
@attribute [Authorize(Roles = nameof(UserRoleEnum.Administrator))]
@inherits AdminBase

<PageTitle>Admin Management</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-3 mb-md-4 fs-3 fs-md-1">⚙️ <span class="d-none d-sm-inline">Admin Management</span><span class="d-sm-none">Admin</span></h1>
            
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading admin data...</p>
                </div>
            }
            else
            {
                <!-- Users Section -->
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <h5 class="mb-2 mb-md-0 d-flex align-items-center">
                                <span class="me-2">👥</span>
                                <span>Users</span>
                            </h5>
                            <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
                                <button class="btn btn-primary" style="min-width: 200px;" @onclick="OpenCreateUserModal">
                                    <i class="bi bi-plus-circle" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-2">Add New User</span>
                                    <span class="d-sm-none ms-2">Add User</span>
                                </button>
                                <button class="btn btn-secondary" style="min-width: 200px;" @onclick="RefreshData">
                                    <i class="bi bi-arrow-clockwise" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-2">Refresh</span>
                                    <span class="d-sm-none ms-2">Refresh</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-1 p-md-3">
                        <SfGrid DataSource="@users" AllowPaging="true" AllowSorting="true" AllowFiltering="true"
                                AllowResizing="true" Height="300" CssClass="mobile-grid">
                            <GridPageSettings PageSize="5"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.FilterBar"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(User.LoginName) HeaderText="Login Name" Width="100" IsPrimaryKey="true"></GridColumn>
                                <GridColumn Field=@nameof(User.UserName) HeaderText="User Name" Width="150"></GridColumn>
                                <GridColumn Field=@nameof(User.RoleDescription) HeaderText="Role" Width="120"></GridColumn>
                                <GridColumn Field=@nameof(User.Note) HeaderText="Note" Width="200"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="200" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var user = (context as User);
                                        }
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-outline-primary" style="width: 50%;" @onclick="() => OpenEditUserModal(user)">
                                                <i class="bi bi-pencil" style="color: inherit;"></i>
@*                                                 <span class="ms-2">Edit</span>
 *@                                            </button>
                                            <button class="btn btn-outline-danger" style="width: 50%;" @onclick="() => DeleteUser(user?.LoginName)">
                                                <i class="bi bi-trash" style="color: inherit;"></i>
@*                                                 <span class="ms-2">Delete</span>
 *@                                            </button>
                                        </div>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>

                <!-- User Roles Section -->
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <h5 class="mb-2 mb-md-0 d-flex align-items-center">
                                <span class="me-2">🔐</span>
                                <span class="d-none d-md-inline">User Roles</span>
                                <span class="d-md-none">Roles</span>
                            </h5>
                            <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
                                <button class="btn btn-primary" style="min-width: 200px;" @onclick="OpenCreateUserRoleModal">
                                    <i class="bi bi-plus-circle" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-2">Add New Role</span>
                                    <span class="d-sm-none ms-2">Add Role</span>
                                </button>
                                <button class="btn btn-secondary" style="min-width: 200px;" @onclick="RefreshData">
                                    <i class="bi bi-arrow-clockwise" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-2">Refresh</span>
                                    <span class="d-sm-none ms-2">Refresh</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-1 p-md-3">
                        <SfGrid DataSource="@userRoles" AllowPaging="true" AllowSorting="true" AllowFiltering="true"
                                AllowResizing="true" Height="300" CssClass="mobile-grid">
                            <GridPageSettings PageSize="5"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.FilterBar"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(UserRole.ID) HeaderText="ID" Width="50" IsPrimaryKey="true"
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn Field=@nameof(UserRole.Description) HeaderText="Description" Width="200"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="100" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var role = (context as UserRole);
                                        }
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-outline-primary" style="width: 50%;" @onclick="() => OpenEditUserRoleModal(role)">
                                                <i class="bi bi-pencil" style="color: inherit;"></i>
@*                                                 <span class="ms-2">Edit</span>
 *@                                            </button>
                                            <button class="btn btn-outline-danger" style="width: 50%;" @onclick="() => DeleteUserRole(role?.ID)">
                                                <i class="bi bi-trash" style="color: inherit;"></i>
@*                                                 <span class="ms-2">Delete</span>
 *@                                            </button>
                                        </div>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>

                <!-- Subjects Section -->
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <h5 class="mb-2 mb-md-0 d-flex align-items-center">
                                <span class="me-2">📚</span>
                                <span>Subjects</span>
                            </h5>
                            <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
                                <button class="btn btn-primary" style="min-width: 200px;" @onclick="OpenCreateSubjectModal">
                                    <i class="bi bi-plus-circle" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-2">Add New Subject</span>
                                    <span class="d-sm-none ms-2">Add Subject</span>
                                </button>
                                <button class="btn btn-secondary" style="min-width: 200px;" @onclick="RefreshData">
                                    <i class="bi bi-arrow-clockwise" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-2">Refresh</span>
                                    <span class="d-sm-none ms-2">Refresh</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-1 p-md-3">
                        <SfGrid DataSource="@subjects" AllowPaging="true" AllowSorting="true" AllowFiltering="true"
                                AllowResizing="true" Height="300" CssClass="mobile-grid">
                            <GridPageSettings PageSize="5"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.FilterBar"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(Subject.SubjectId) HeaderText="ID" Width="50" IsPrimaryKey="true" 
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn Field=@nameof(Subject.SubjectName) HeaderText="Subject Name" Width="200"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="100" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var subject = (context as Subject);
                                        }
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-outline-primary" style="width: 50%;" @onclick="() => OpenEditSubjectModal(subject)"
                                                    title="Edit">
                                                <i class="bi bi-pencil" style="color: inherit;"></i>
@*                                                 <span class="ms-2">Edit</span>
 *@                                            </button>
                                            <button class="btn btn-outline-danger" style="width: 50%;" @onclick="() => DeleteSubject(subject)"
                                                    title="Delete">
                                                <i class="bi bi-trash" style="color: inherit;"></i>
@*                                                 <span class="ms-2">Delete</span>
 *@                                            </button>
                                        </div>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>

                <!-- Locations Section -->
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <h5 class="mb-2 mb-md-0 d-flex align-items-center">
                                <span class="me-2">📍</span>
                                <span>Locations</span>
                            </h5>
                            <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
                                <button class="btn btn-primary" style="min-width: 200px;" @onclick="OpenCreateLocationModal">
                                    <i class="bi bi-plus-circle" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-2">Add New Location</span>
                                    <span class="d-sm-none ms-2">Add Location</span>
                                </button>
                                <button class="btn btn-secondary" style="min-width: 200px;" @onclick="RefreshData">
                                    <i class="bi bi-arrow-clockwise" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-2">Refresh</span>
                                    <span class="d-sm-none ms-2">Refresh</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-1 p-md-3">
                        <SfGrid DataSource="@locations" AllowPaging="true" AllowSorting="true" AllowFiltering="true"
                                AllowResizing="true" Height="300" CssClass="mobile-grid">
                            <GridPageSettings PageSize="5"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.FilterBar"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(Location.LocationId) HeaderText="ID" Width="50" IsPrimaryKey="true" 
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn Field=@nameof(Location.LocationName) HeaderText="Location Name" Width="200"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="100" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var location = (context as Location);
                                        }
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-outline-primary" style="width: 50%;" @onclick="() => OpenEditLocationModal(location)"
                                                    title="Edit">
                                                <i class="bi bi-pencil" style="color: inherit;"></i>
@*                                                 <span class="ms-2">Edit</span>
 *@                                            </button>
                                            <button class="btn btn-outline-danger" style="width: 50%;" @onclick="() => DeleteLocation(location)"
                                                    title="Delete">
                                                <i class="bi bi-trash" style="color: inherit;"></i>
@*                                                 <span class="ms-2">Delete</span>
 *@                                            </button>
                                        </div>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- User Modal -->
<SfDialog @bind-Visible="showUserModal" Header="@userModalTitle" Width="500px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentUser" OnValidSubmit="@SaveUser">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />

                <div class="mb-3">
                    <label class="form-label">Login Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentUser.LoginName" Placeholder="Enter login name"
                               CssClass="form-control" Enabled="@(!isEditUserMode)"></SfTextBox>
                    <ValidationMessage For="@(() => currentUser.LoginName)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">User Name</label>
                    <SfTextBox @bind-Value="currentUser.UserName" Placeholder="Enter user name"
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentUser.UserName)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Password</label>
                    <SfTextBox @bind-Value="currentUser.Password" Type="InputType.Password"
                               Placeholder="@GetPasswordPlaceholder()" CssClass="form-control"
                               @onblur="ValidatePassword"></SfTextBox>
                    <ValidationMessage For="@(() => currentUser.Password)" />
                    @if (passwordValidationErrors.Any())
                    {
                        <div class="password-validation text-danger">
                            @foreach (var error in passwordValidationErrors)
                            {
                                <small>@error</small>
                            }
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(currentUser.Password) && passwordValidationErrors.Count == 0)
                    {
                        <div class="password-validation text-success">
                            <small>✓ Password meets requirements</small>
                        </div>
                    }
                </div>

                <div class="mb-3">
                    <label class="form-label">Role</label>
                    <SfDropDownList TValue="int?" TItem="UserRole" @bind-Value="currentUser.RoleId"
                                    DataSource="@userRoles" CssClass="form-control" Placeholder="Select a role">
                        <DropDownListFieldSettings Value="ID" Text="Description"></DropDownListFieldSettings>
                        <DropDownListEvents TValue="int?" TItem="UserRole" ValueChange="@OnRoleChanged"></DropDownListEvents>
                    </SfDropDownList>
                    <ValidationMessage For="@(() => currentUser.RoleId)" />
                </div>

                @if (!isEditUserMode && showAssignmentSection)
                {
                    <div class="mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">👤 Assign to Person</h6>
                                <small class="text-muted">Link this user account to an existing tutor or student</small>
                            </div>
                            <div class="card-body">
                                @if (GetSelectedRoleDescription() == UserRoleEnum.Tutor.ToString())
                                {
                                    <div class="mb-3">
                                        <label class="form-label">Select Tutor</label>
                                        <SfDropDownList TValue="int?" TItem="Tutor" @bind-Value="selectedTutorId"
                                                        DataSource="@GetAvailableTutors()" CssClass="form-control"
                                                        Placeholder="Select a tutor to link">
                                            <DropDownListFieldSettings Value="TutorId" Text="TutorName"></DropDownListFieldSettings>
                                        </SfDropDownList>
                                        <small class="form-text text-muted">This will update the tutor's login name to match this user</small>
                                    </div>
                                }
                                else if (GetSelectedRoleDescription() == UserRoleEnum.Student.ToString())
                                {
                                    <div class="mb-3">
                                        <label class="form-label">Select Student</label>
                                        <SfDropDownList TValue="int?" TItem="Student" @bind-Value="selectedStudentId"
                                                        DataSource="@GetAvailableStudents()" CssClass="form-control"
                                                        Placeholder="Select a student to link">
                                            <DropDownListFieldSettings Value="StudentId" Text="StudentName"></DropDownListFieldSettings>
                                        </SfDropDownList>
                                        <small class="form-text text-muted">This will update the student's login name to match this user</small>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                }

                <div class="mb-3">
                    <label class="form-label">Note</label>
                    <SfTextBox @bind-Value="currentUser.Note" Placeholder="Enter note"
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentUser.Note)" />
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-blue-custom" type="submit" Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        else
                        {
                            <i class="bi @(isEditUserMode ? "bi-pencil" : "bi-plus-circle")" style="color: white;"></i><span class="ms-2">@(isEditUserMode ? "Update" : "Create")</span>
                        }
                    </SfButton>
                    <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseUserModal">
                        <i class="bi bi-x-circle" style="color: inherit;"></i><span class="ms-2">Cancel</span>
                    </SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- UserRole Modal -->
<SfDialog @bind-Visible="showUserRoleModal" Header="@userRoleModalTitle" Width="450px" Height="auto" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentUserRole" OnValidSubmit="@SaveUserRole">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />

                <div class="mb-3">
                    <label class="form-label">ID <span class="text-danger">*</span></label>
                    <SfNumericTextBox TValue="int" @bind-Value="currentUserRole.ID"
                                      Placeholder="Enter role ID" CssClass="form-control"
                                      Readonly="@isEditUserRoleMode" Min="1" Max="999"
                                      ShowSpinButton="false" Format="0"></SfNumericTextBox>
                    <ValidationMessage For="@(() => currentUserRole.ID)" />
                    @if (!isEditUserRoleMode)
                    {
                        <small class="text-muted">Choose a unique ID number for this role (suggested: @currentUserRole.ID)</small>
                    }
                    else
                    {
                        <small class="text-muted">Role ID cannot be changed when editing</small>
                    }
                </div>

                <div class="mb-3">
                    <label class="form-label">Description <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentUserRole.Description" Placeholder="Enter role description"
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentUserRole.Description)" />
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-blue-custom" type="submit" Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        else
                        {
                            <i class="bi @(isEditUserRoleMode ? "bi-pencil" : "bi-plus-circle")" style="color: white;"></i><span class="ms-2">@(isEditUserRoleMode ? "Update" : "Create")</span>
                        }
                    </SfButton>
                    <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseUserRoleModal">
                        <i class="bi bi-x-circle" style="color: inherit;"></i><span class="ms-2">Cancel</span>
                    </SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Subject Modal -->
<SfDialog @bind-Visible="showSubjectModal" Header="@subjectModalTitle" Width="400px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentSubject" OnValidSubmit="@SaveSubject">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                
                <div class="mb-3">
                    <label class="form-label">Subject Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentSubject.SubjectName" Placeholder="Enter subject name" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentSubject.SubjectName)" />
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-blue-custom" type="submit" Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        else
                        {
                            <i class="bi @(isEditSubjectMode ? "bi-pencil" : "bi-plus-circle")" style="color: white;"></i><span class="ms-2">@(isEditSubjectMode ? "Update" : "Create")</span>
                        }
                    </SfButton>
                    <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseSubjectModal">
                        <i class="bi bi-x-circle" style="color: inherit;"></i><span class="ms-2">Cancel</span>
                    </SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Location Modal -->
<SfDialog @bind-Visible="showLocationModal" Header="@locationModalTitle" Width="400px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentLocation" OnValidSubmit="@SaveLocation">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                
                <div class="mb-3">
                    <label class="form-label">Location Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentLocation.LocationName" Placeholder="Enter location name" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentLocation.LocationName)" />
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-blue-custom" type="submit" Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        else
                        {
                            <i class="bi @(isEditLocationMode ? "bi-pencil" : "bi-plus-circle")" style="color: white;"></i><span class="ms-2">@(isEditLocationMode ? "Update" : "Create")</span>
                        }
                    </SfButton>
                    <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseLocationModal">
                        <i class="bi bi-x-circle" style="color: inherit;"></i><span class="ms-2">Cancel</span>
                    </SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Delete Confirmation Dialog -->
<DeleteConfirmationDialog />

<!-- Alert Dialog -->
<AlertDialog />


