@page "/lessons"
@using ShiningCMusicCommon.Models
@using ShiningCMusicCommon.Enums
@using ShiningCMusicCommon.Constants
@using ShiningCMusicCommon.Extensions
@using ShiningCMusicApp.Services
@using ShiningCMusicApp.Services.Interfaces
@using ShiningCMusicApp.Components
@using Syncfusion.Blazor.Schedule
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Calendars
@using System.Security.Claims
@using System.Globalization
@attribute [Authorize]

<PageTitle>Lesson Time Table</PageTitle>

<div class="container-fluid">
    <!-- MediaQuery component for responsive view switching -->
    <SfMediaQuery @bind-ActiveBreakpoint="activeBreakpoint" OnBreakpointChanged="OnBreakpointChanged"></SfMediaQuery>
    <div class="row">
        <div class="col-12">
            <AuthorizeView>
                <Authorized>
                    <h1 class="mb-3 mb-md-4 fs-3 fs-md-1">🎵 <span class="d-none d-sm-inline">@GetPageTitle(context.User)</span><span class="d-sm-none">@GetShortPageTitle(context.User)</span></h1>
                </Authorized>
                <NotAuthorized>
                    <h1 class="mb-3 mb-md-4 fs-3 fs-md-1">🎵 <span class="d-none d-sm-inline">Lesson Time Table</span><span class="d-sm-none">Lessons</span></h1>
                </NotAuthorized>
            </AuthorizeView>

            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading lessons...</p>
                </div>
            }
            else
            {
                <AuthorizeView Roles="@UserRoleEnum.Administrator.ToString()">
                    @if (tutors.Any())
                    {
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <button class="btn btn-link p-0 text-decoration-none text-start w-100 d-flex align-items-center justify-content-between"
                                    type="button"
                                    data-bs-toggle="collapse"
                                    data-bs-target="#tutorColorsCollapse"
                                    aria-expanded="false"
                                    aria-controls="tutorColorsCollapse">
                                        <span>
                                            <i class="bi bi-palette"></i>
                                            <span class="d-none d-sm-inline">Tutor Colors & Filtering</span>
                                            <span class="d-sm-none">Colors & Filter</span>
                                        </span>
                                        <i class="bi bi-chevron-down"></i>
                                    </button>
                                </h6>
                            </div>
                            <div class="collapse @(IsDesktop ? "show" : "")" id="tutorColorsCollapse">
                                <div class="card-body">
                                    <!-- Show All Tutors Button and Selected Count -->
                                    @if (selectedTutorIds.Any())
                                    {
                                        <div class="row mb-3">
                                            <div class="col-12">
                                                <div class="d-flex align-items-center gap-2 flex-wrap">
                                                    <button class="btn btn-outline-secondary btn-sm" @onclick="ClearTutorFilter">
                                                        <i class="bi bi-x-circle me-1"></i>Show All Tutors
                                                    </button>
                                                    <small class="text-primary">
                                                        <i class="bi bi-funnel-fill me-1"></i>
                                                        @selectedTutorIds.Count tutor@(selectedTutorIds.Count == 1 ? "" : "s") selected
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    }

                                    <!-- Tutor Colors Section -->
                                    <div class="row">
                                        @foreach (var tutor in tutors)
                                        {
                                            <div class="col-6 col-md-4 col-lg-3 mb-3">
                                                <div class="d-flex align-items-center">
                                                    <SfColorPicker Value="@(tutor.Color ?? "#6C757D")"
                                                    ShowButtons="true"
                                                    Mode="ColorPickerMode.Picker"
                                                    ModeSwitcher="false"
                                                    ValueChange="@((ColorPickerEventArgs args) => OnTutorColorChanged(tutor.TutorId, args.CurrentValue.Hex))"
                                                    CssClass="me-2">
                                                    </SfColorPicker>
                                                    <small class="@(selectedTutorIds.Contains(tutor.TutorId) ? "fw-bold text-primary" : "text-muted") tutor-name-clickable"
                                                          @onclick="() => OnTutorNameClicked(tutor.TutorId)"
                                                          style="cursor: pointer; user-select: none;"
                                                          title="Click to @(selectedTutorIds.Contains(tutor.TutorId) ? "deselect" : "select") @tutor.TutorName">
                                                        @tutor.TutorName
                                                    </small>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                    <small class="text-info">
                                        <i class="bi bi-info-circle"></i>
                                        <span class="d-none d-sm-inline">Click tutor names to filter lessons (multiple selection allowed). Select a color and click Apply to update all lessons for that tutor.</span>
                                        <span class="d-sm-none">Click names to filter. Select color and Apply.</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    }
                </AuthorizeView>

                <div class="card">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            @if (scheduleEvents.Any())
                            {
                                <h6 class="mb-2 mb-md-0 text-truncate">
                                    @if (NextLesson != null)
                                    {
                                        <span class="d-none d-lg-inline">Next lesson: @NextLesson.Subject at @NextLesson.StartTime.ToString("dd/MM/yyyy HH:mm", CultureInfo.InvariantCulture)</span>
                                        <span class="d-lg-none">Next: @NextLesson.Subject</span>
                                    }
                                    else
                                    {
                                        <span class="d-none d-lg-inline">No upcoming lessons</span>
                                        <span class="d-lg-none">No upcoming</span>
                                    }
                                    @if (selectedTutorIds.Any())
                                    {
                                        var selectedTutorNames = tutors.Where(t => selectedTutorIds.Contains(t.TutorId)).Select(t => t.TutorName).ToList();
                                        <br />
                                        <small class="text-primary">
                                            <i class="bi bi-funnel-fill me-1"></i>
                                            Filtered: @string.Join(", ", selectedTutorNames) (@scheduleEvents.Count lessons)
                                        </small>
                                    }
                                </h6>
                            }
                            else if (selectedTutorIds.Any())
                            {
                                var selectedTutorNames = tutors.Where(t => selectedTutorIds.Contains(t.TutorId)).Select(t => t.TutorName).ToList();
                                <h6 class="mb-2 mb-md-0 text-muted">
                                    <i class="bi bi-funnel-fill me-1"></i>
                                    No lessons found for @string.Join(", ", selectedTutorNames)
                                </h6>
                            }
                            <div class="d-flex gap-2 flex-wrap justify-content-end">
                                <button class="btn btn-primary lesson-action-btn" @onclick="ToggleScheduleView">
                                    <i class="bi @(currentView == View.Agenda ? "bi-calendar-fill" : "bi-list-ul")" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-2">@(currentView == View.Agenda ? "Calendar" : "List")</span>
                                    <span class="d-sm-none ms-2">@(currentView == View.Agenda ? "Calendar" : "List")</span>
                                </button>
                                <button class="btn btn-secondary btn-sm lesson-action-btn" @onclick="RefreshData">
                                    <i class="bi bi-arrow-clockwise" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-1">Refresh</span>
                                    <span class="d-sm-none ms-2">Refresh</span>
                                </button>

                                <!-- Import/Export Buttons -->
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-download"></i>
                                        <span class="d-none d-sm-inline ms-1">Export</span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" @onclick="OnExportToIcs" @onclick:preventDefault="true">
                                                <i class="bi bi-calendar-event me-2"></i>Export to ICS
                                            </a></li>
                                    </ul>
                                </div>

                                @if (CanAddEvents)
                                {
                                    <div class="import-button-container">
                                        <input type="file"
                                        id="icsFileInput"
                                        accept=".ics"
                                        @onchange="OnFileInputChange" />
                                        <button type="button"
                                        class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-upload"></i>
                                            <span class="d-none d-sm-inline ms-1">Import</span>
                                        </button>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-1 p-md-3">
                        <!-- Always render the schedule component, but hide it in mobile list view -->
                            <div class="schedule-container">
                            <SfSchedule TValue="ScheduleEvent"
                            @bind-SelectedDate="@selectedDate"
                            Height="calc(100vh - 8rem)"
                            @bind-CurrentView="@currentView"
                            FirstDayOfWeek="1"
                            StartHour="08:00"
                            EndHour="22:30"
                            AllowDragAndDrop="@CanEditEvents"
                            @ref="scheduleRef"
                            CssClass="mobile-schedule">

                                <ScheduleEvents TValue="ScheduleEvent"
                                OnActionBegin="OnActionBegin"
                                ActionCompleted="OnActionComplete"
                                EventRendered="OnEventRendered"
                                OnCellClick="OnCellClick"
                                OnPopupOpen="OnPopupOpen"
                                Dragged="OnDragged"></ScheduleEvents>

                                <ScheduleTimeScale Enable="true" Interval="30" SlotCount="2"></ScheduleTimeScale>

                                <ScheduleEventSettings DataSource="@scheduleEvents"
                                TValue="ScheduleEvent"
                                AllowAdding="@CanAddEvents"
                                AllowEditing="@CanEditEvents"
                                AllowDeleting="@CanDeleteEvents"
                                AllowEditFollowingEvents="true">
                                    <Template>
                                        @{
                                            var eventData = context as ScheduleEvent;
                                        }
                                        <div class="template-wrap">
                                            <div class="subject">@(eventData?.StudentName)</div>
                                            @if (!string.IsNullOrEmpty(eventData?.TutorName))
                                            {
                                                <div class="tutor">
                                                    <i class="bi bi-person-fill"></i>@(eventData.TutorName)
                                                </div>
                                            }
                                            @if (!string.IsNullOrEmpty(eventData?.SubjectName))
                                            {
                                                <div class="student">
                                                    <i class="bi bi-book-fill"></i>@(eventData.SubjectName)
                                                </div>
                                            }
                                        </div>
                                    </Template>
                                </ScheduleEventSettings>

                                <ScheduleTemplates>
                                    <EditorTemplate Context="eventData">
                                        @{
                                            var scheduleEvent = eventData as ScheduleEvent ?? new ScheduleEvent();
                                            var currentAction = scheduleRef?.GetCurrentAction();
                                            currentEditingEvent = scheduleEvent;
                                        }
                                        <div class="custom-event-editor">
                                            <div class="row">
                                                <div class="col-md-12 mb-3">
                                                    <label class="form-label">Subject <span class="text-danger">*</span></label>
                                                    <select class="form-select" @bind="scheduleEvent.SubjectId" @bind:after="OnSubjectSelectionChanged">
                                                        <option value="0">Select Subject First</option>
                                                        @foreach (var subject in subjects)
                                                        {
                                                            <option value="@subject.SubjectId">@subject.SubjectName</option>
                                                        }
                                                    </select>
                                                    <small class="text-muted">Select a subject to filter available students and tutors</small>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">Student <span class="text-danger">*</span></label>
                                                    <select class="form-select" @bind="scheduleEvent.StudentId" @bind:after="OnStudentChanged" disabled="@(scheduleEvent.SubjectId == 0)">
                                                        <option value="0">@(scheduleEvent.SubjectId == 0 ? "Select Subject First" : "Select Student")</option>
                                                        @foreach (var student in GetFilteredStudents())
                                                        {
                                                            <option value="@student.StudentId">@student.StudentName</option>
                                                        }
                                                    </select>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">Tutor <span class="text-danger">*</span></label>
                                                    <select class="form-select" @bind="scheduleEvent.TutorId" @bind:after="OnTutorChanged" disabled="@(scheduleEvent.SubjectId == 0)">
                                                        <option value="0">@(scheduleEvent.SubjectId == 0 ? "Select Subject First" : "Select Tutor")</option>
                                                        @foreach (var tutor in GetFilteredTutors())
                                                        {
                                                            <option value="@tutor.TutorId">@tutor.TutorName</option>
                                                        }
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">Location</label>
                                                    <select class="form-select" @bind="scheduleEvent.LocationId" @bind:after="OnLocationChanged">
                                                        <option value="0">Select Location</option>
                                                        @foreach (var location in locations)
                                                        {
                                                            <option value="@location.LocationId">@location.LocationName</option>
                                                        }
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">Start Time</label>
                                                    <SfDateTimePicker TValue="DateTime"
                                                    @bind-Value="scheduleEvent.StartTime"
                                                    CssClass="form-control"
                                                    Format="dd/MM/yyyy HH:mm"
                                                    Step="15">
                                                    </SfDateTimePicker>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">End Time</label>
                                                    <SfDateTimePicker TValue="DateTime"
                                                    @bind-Value="scheduleEvent.EndTime"
                                                    CssClass="form-control"
                                                    Format="dd/MM/yyyy HH:mm"
                                                    Step="15">
                                                    </SfDateTimePicker>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">All Day</label>
                                                    <div class="form-check">
                                                        <input type="checkbox" class="form-check-input" @bind="scheduleEvent.IsAllDay" />
                                                        <label class="form-check-label">All Day Event</label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-12 mb-3">
                                                    <label class="form-label">Description</label>
                                                    <textarea class="form-control" rows="3" @bind="scheduleEvent.Description" placeholder="Enter lesson description"></textarea>
                                                </div>
                                            </div>

                                            <!-- Recurrence Section - Hidden when editing single occurrence -->
                                            @if (currentAction != CurrentAction.EditOccurrence)
                                            {
                                                <div class="row" id="recurrence-section">
                                                    <div class="col-md-12 mb-3">
                                                        <label class="form-label fw-semibold">
                                                            <i class="bi bi-arrow-repeat me-1"></i>Recurrence Pattern
                                                        </label>
                                                        <small class="text-muted d-block mb-2">Create multiple lessons automatically based on a pattern</small>
                                                        <SfRecurrenceEditor @bind-Value="scheduleEvent.RecurrenceRule"
                                                        @bind-Value:after="OnRecurrenceValueChanged"
                                                        StartDate="scheduleEvent.StartTime"
                                                        Frequencies="@AllowedFrequencies"
                                                        EndTypes="@AllowedEndTypes"
                                                        CssClass="custom-recurrence-editor">
                                                        </SfRecurrenceEditor>
                                                    </div>
                                                </div>
                                            }
                                            else
                                            {
                                                <!-- Show info message when editing single occurrence -->
                                                <div class="row">
                                                    <div class="col-md-12 mb-3">
                                                        <div class="alert alert-info">
                                                            <i class="bi bi-info-circle me-2"></i>
                                                            <strong>Editing Single Occurrence:</strong> You are editing only this specific lesson. To modify the entire recurring series, please edit the series instead.
                                                        </div>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </EditorTemplate>
                                    <EditorFooterTemplate>
                                        <div class="custom-editor-footer">
                                            <AuthorizeView Roles="@UserRoleEnum.Administrator.ToString()">
                                                <Authorized Context="authContext">
                                                    <button type="button" class="btn btn-primary me-2" style="min-width: 90px;" @onclick="OnSaveClick">
                                                        <i class="bi bi-check-circle me-1"></i>Save
                                                    </button>
                                                    <button type="button" class="btn btn-secondary" style="min-width: 90px;" @onclick="OnCancelClick">
                                                        <i class="bi bi-x-circle me-1"></i>Cancel
                                                    </button>
                                                </Authorized>
                                                <NotAuthorized Context="authContext">
                                                    <button type="button" class="btn btn-secondary" style="min-width: 90px;" @onclick="OnCancelClick">
                                                        <i class="bi bi-x-circle me-1"></i>Close
                                                    </button>
                                                    <div class="text-muted mt-2">
                                                        <small>You can only view lesson details. Contact an administrator to make changes.</small>
                                                    </div>
                                                </NotAuthorized>
                                            </AuthorizeView>
                                        </div>
                                    </EditorFooterTemplate>
                                </ScheduleTemplates>

                                <ScheduleViews>
                                    <ScheduleView Option="View.Day"></ScheduleView>
                                    <ScheduleView Option="View.Week"></ScheduleView>
                                    <ScheduleView Option="View.Month"></ScheduleView>
                                    <ScheduleView Option="View.Agenda"></ScheduleView>
                                </ScheduleViews>

                                <ScheduleQuickInfoTemplates TemplateType="TemplateType.Event">
                                    <ContentTemplate>
                                        @{
                                            var eventData = context as ScheduleEvent;
                                        }
                                        <div class="quick-info">
                                            <div class="event-title">@(eventData?.SubjectName)</div>
                                            <div class="event-details">
                                                <p>
                                                    <i class="bi bi-clock"></i>
                                                    @(eventData?.StartTime.ToString("MMM dd, yyyy h:mm tt")) - @(eventData?.EndTime.ToString("h:mm tt"))
                                                </p>
                                                @*
                                            @if (!string.IsNullOrEmpty(eventData?.SubjectName))
                                            {
                                                <p>
                                                    <i class="bi bi-book"></i>
                                                    <strong>Subject:</strong> @(eventData.SubjectName)
                                                </p>
                                            }
                                            *@
                                                @if (!string.IsNullOrEmpty(eventData?.TutorName))
                                                {
                                                    <p>
                                                        <i class="bi bi-person-fill"></i>
                                                        <strong>Tutor:</strong> @(eventData.TutorName)
                                                    </p>
                                                }

                                                @if (!string.IsNullOrEmpty(eventData?.Location))
                                                {
                                                    <p>
                                                        <i class="bi bi-geo-alt"></i>
                                                        <strong>Location:</strong> @(eventData.Location)
                                                    </p>
                                                }
                                                @if (!string.IsNullOrEmpty(eventData?.Description))
                                                {
                                                    <p>
                                                        <i class="bi bi-card-text"></i>
                                                        <strong>Description:</strong> @(eventData.Description)
                                                    </p>
                                                }
                                            </div>
                                        </div>
                                    </ContentTemplate>
                                </ScheduleQuickInfoTemplates>

                            </SfSchedule>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Delete Confirmation Dialog -->
<DeleteConfirmationDialog />

<!-- Alert Dialog -->
<AlertDialog />

@inherits LessonsBase