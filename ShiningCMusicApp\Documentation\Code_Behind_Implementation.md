# Code-Behind File Implementation for Razor Pages

## Overview
This document explains how to implement code-behind files for Razor pages in Blazor applications. Code-behind files separate the C# logic from the Razor markup, improving code organization, maintainability, and testability.

## ✅ IMPLEMENTATION COMPLETED

### What Was Implemented

**✅ Lessons Page Code-Behind:**
- Created `Lessons.razor.cs` with all C# logic from `Lessons.razor`
- Split into multiple partial class files for better organization:
  - `Lessons.razor.cs` - Main class with core functionality
  - `Lessons.razor.Methods.cs` - Event handlers and UI interaction methods
  - `Lessons.razor.ICS.cs` - ICS import/export functionality
- Moved all properties, methods, and event handlers to the base class
- Maintained all existing functionality including responsive behavior, CRUD operations, and ICS import/export
- Updated `Lessons.razor` to inherit from `LessonsBase`
- Files are properly nested under the Razor file in Solution Explorer

**✅ Home Page Code-Behind (Example):**
- Created `Home.razor.cs` as a simple example
- Demonstrated basic navigation methods
- Updated `Home.razor` to inherit from `HomeBase`
- File is properly nested under the Razor file in Solution Explorer

## Benefits of Code-Behind Pattern

### 1. **Separation of Concerns**
- **Markup**: Razor files contain only HTML/Razor markup and component declarations
- **Logic**: C# files contain all business logic, event handlers, and data processing
- **Cleaner Code**: Easier to read and maintain both markup and logic separately

### 2. **Improved Testability**
- **Unit Testing**: Code-behind classes can be easily unit tested
- **Mocking**: Dependencies can be mocked for isolated testing
- **Test Coverage**: Better test coverage of business logic

### 3. **Better IntelliSense and Debugging**
- **Full IntelliSense**: Complete C# IntelliSense support in code-behind files
- **Debugging**: Easier debugging with proper breakpoint support
- **Refactoring**: Better refactoring support in IDEs

### 4. **Code Reusability**
- **Inheritance**: Multiple pages can inherit from the same base class
- **Shared Logic**: Common functionality can be extracted to base classes
- **Polymorphism**: Different implementations can override base methods

## Implementation Pattern

### Step 1: Create the Code-Behind Base Class

```csharp
// Pages/PageName.razor.cs
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace ShiningCMusicApp.Pages;

public class PageNameBase : ComponentBase
{
    [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;
    [Inject] protected NavigationManager Navigation { get; set; } = default!;

    // Add other injected services as needed

    // Protected properties (accessible from Razor page)
    protected string someProperty = "default value";

    // Protected methods (accessible from Razor page)
    protected async Task SomeMethod()
    {
        // Implementation
    }

    // Lifecycle methods
    protected override async Task OnInitializedAsync()
    {
        // Initialization logic
    }
}
```

### Step 2: Update the Razor Page

```razor
@page "/pagename"
@attribute [Authorize]

<!-- Remove @inject directives - they're now in the base class -->
<!-- Remove @code blocks - they're now in the base class -->

<!-- Your HTML/Razor markup here -->
<div>
    <h1>@someProperty</h1>
    <button @onclick="SomeMethod">Click Me</button>
</div>

@inherits PageNameBase
```

### Step 3: Configure Project File for Nesting

Add this to your `.csproj` file to nest code-behind files under Razor files:

```xml
<ItemGroup>
  <Compile Update="Pages\PageName.razor.cs">
    <DependentUpon>PageName.razor</DependentUpon>
  </Compile>
  <!-- For partial classes -->
  <Compile Update="Pages\PageName.razor.Methods.cs">
    <DependentUpon>PageName.razor</DependentUpon>
  </Compile>
  <Compile Update="Pages\PageName.razor.Services.cs">
    <DependentUpon>PageName.razor</DependentUpon>
  </Compile>
</ItemGroup>
```

### Step 4: Key Points to Remember

1. **Remove @inject directives** from Razor files - move them to base class as [Inject] properties
2. **Remove @code blocks** from Razor files - move all C# code to base class
3. **Use protected access modifiers** for properties and methods that need to be accessible from Razor markup
4. **Add @inherits directive** at the end of the Razor file
5. **Keep using statements** in Razor files for components and models used in markup
6. **Use partial classes** for large code-behind files to improve organization

## Example: Lessons Page Implementation

### Before (All in Lessons.razor)
```razor
@page "/lessons"
@inject ILessonApiService LessonApi
@inject IJSRuntime JSRuntime

<div>
    <!-- 400+ lines of HTML markup -->
</div>

@code {
    private List<ScheduleEvent> scheduleEvents = new();
    private bool isLoading = true;
    
    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }
    
    private async Task LoadData()
    {
        // 50+ lines of logic
    }
    
    // 100+ more methods...
}
```

### After (Separated)

**Lessons.razor** (Clean markup only):
```razor
@page "/lessons"
@attribute [Authorize]

<div>
    <!-- 400+ lines of HTML markup -->
</div>

@inherits LessonsBase
```

**Lessons.razor.cs** (Main logic):
```csharp
public partial class LessonsBase : ComponentBase, IDisposable
{
    [Inject] protected ILessonApiService LessonApi { get; set; } = default!;
    [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;

    protected List<ScheduleEvent> scheduleEvents = new();
    protected bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    protected async Task LoadData()
    {
        // 50+ lines of logic
    }

    // Core methods...

    public void Dispose()
    {
        // Cleanup logic
    }
}
```

**Lessons.razor.Methods.cs** (Event handlers):
```csharp
public partial class LessonsBase
{
    protected async Task OnSaveClick()
    {
        // Save logic
    }

    protected async Task OnEventClick(EventClickArgs<ScheduleEvent> args)
    {
        // Event click handling
    }

    // More event handlers...
}
```

**Lessons.razor.ICS.cs** (ICS functionality):
```csharp
public partial class LessonsBase
{
    protected async Task OnExportToIcs()
    {
        // Export logic
    }

    protected async Task ProcessIcsFile(string fileName, string fileContent)
    {
        // Import logic
    }

    // More ICS methods...
}
```

## Best Practices

### 1. **Naming Convention**
- Code-behind file: `PageName.razor.cs`
- Razor page: `PageName.razor`
- Place both files in the same folder
- For large pages, consider splitting into partial classes:
  - `PageName.razor.cs` - Main class
  - `PageName.razor.Methods.cs` - Event handlers
  - `PageName.razor.Services.cs` - Service methods
- Configure project file to nest code-behind files under Razor files

### 2. **Access Modifiers**
- Use `protected` for members accessed from Razor markup
- Use `private` for internal implementation details
- Use `public` only for members that need external access

### 3. **Dependency Injection**
- Move all `@inject` directives to `[Inject]` properties in base class
- Use `= default!` to suppress nullable warnings for injected properties

### 4. **Event Handlers**
- Keep event handler methods in base class
- Use descriptive method names that clearly indicate their purpose

### 5. **Lifecycle Methods**
- Override lifecycle methods in base class
- Call `base.MethodName()` when overriding if base implementation is needed

## Testing Benefits

With code-behind files, you can easily create unit tests:

```csharp
[Test]
public async Task LoadData_ShouldPopulateScheduleEvents()
{
    // Arrange
    var mockLessonApi = new Mock<ILessonApiService>();
    var lessonsBase = new LessonsBase();
    // Inject mocked dependencies
    
    // Act
    await lessonsBase.LoadData();
    
    // Assert
    Assert.IsNotEmpty(lessonsBase.scheduleEvents);
}
```

## Migration Checklist

When converting existing Razor pages to use code-behind:

- [ ] Create new `PageName.razor.cs` file
- [ ] Move all `@inject` directives to `[Inject]` properties
- [ ] Move all `@code` blocks to base class methods
- [ ] Change access modifiers to `protected` for Razor-accessible members
- [ ] Add `@inherits PageNameBase` to Razor file
- [ ] Remove `@code` blocks from Razor file
- [ ] Update project file to nest code-behind files under Razor files
- [ ] For large files, consider splitting into partial classes
- [ ] Test all functionality to ensure nothing is broken
- [ ] Update any unit tests to use the new base class

## Conclusion

The code-behind pattern significantly improves code organization and maintainability in Blazor applications. It provides clear separation between presentation and logic, making the codebase easier to understand, test, and maintain.
